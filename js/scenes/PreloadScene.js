class PreloadScene extends Phaser.Scene {
    constructor() {
        super({ key: 'PreloadScene' });
    }
    
    preload() {
        console.log('PreloadScene: Starting preload...');

        try {
            // Create loading bar
            this.createLoadingBar();

            // Generate game assets programmatically since we don't have image files
            this.generateAssets();

            // Load any external assets here if needed
            // this.load.image('background', 'assets/images/background.png');

            // Set up loading events
            this.load.on('progress', this.updateLoadingBar, this);
            this.load.on('complete', this.loadComplete, this);

            console.log('PreloadScene: Preload setup complete');
        } catch (error) {
            console.error('PreloadScene: Error during preload setup:', error);
            // Try to continue to main menu anyway
            setTimeout(() => {
                this.scene.start('MainMenuScene');
            }, 1000);
        }
    }
    
    createLoadingBar() {
        const width = this.cameras.main.width;
        const height = this.cameras.main.height;
        
        // Background
        this.add.rectangle(width / 2, height / 2, width, height, 0x2C3E50);
        
        // Title
        this.add.text(width / 2, height / 2 - 100, 'WATER POLO CLASH', {
            fontSize: '48px',
            fontFamily: 'Arial',
            color: '#ffffff',
            fontStyle: 'bold'
        }).setOrigin(0.5);
        
        // Loading text
        this.loadingText = this.add.text(width / 2, height / 2 + 50, 'Loading...', {
            fontSize: '24px',
            fontFamily: 'Arial',
            color: '#ffffff'
        }).setOrigin(0.5);
        
        // Progress bar background
        this.progressBarBg = this.add.rectangle(width / 2, height / 2 + 100, 400, 20, 0x34495E);
        
        // Progress bar fill
        this.progressBar = this.add.rectangle(width / 2 - 200, height / 2 + 100, 0, 16, 0x3498DB);
        this.progressBar.setOrigin(0, 0.5);
    }
    
    updateLoadingBar(progress) {
        this.progressBar.width = 400 * progress;
        this.loadingText.setText(`Loading... ${Math.round(progress * 100)}%`);
    }
    
    loadComplete() {
        console.log('PreloadScene: Loading complete!');
        this.loadingText.setText('Loading Complete!');

        // Transition to main menu after a short delay
        this.time.delayedCall(1000, () => {
            console.log('PreloadScene: Starting MainMenuScene...');
            this.scene.start('MainMenuScene');
        });
    }
    
    generateAssets() {
        try {
            console.log('PreloadScene: Generating assets...');

            // Generate player sprites
            this.generatePlayerSprites();

            // Generate ball sprite
            this.generateBallSprite();

            // Generate UI elements
            this.generateUIElements();

            // Generate pool elements
            this.generatePoolElements();

            // Generate power-up sprites
            this.generatePowerUpSprites();

            console.log('PreloadScene: Assets generated successfully');
        } catch (error) {
            console.error('PreloadScene: Error generating assets:', error);
            // Continue anyway - the game might still work with missing assets
        }
    }
    
    generatePlayerSprites() {
        // Create player sprites for both teams
        const playerRadius = GameConfig.PLAYER.RADIUS;
        const diameter = playerRadius * 2;
        
        // Team 1 (Blue)
        const team1Graphics = this.add.graphics();
        team1Graphics.fillStyle(GameConfig.PLAYER.TEAM_1_COLOR);
        team1Graphics.fillCircle(playerRadius, playerRadius, playerRadius);
        team1Graphics.lineStyle(2, 0xFFFFFF);
        team1Graphics.strokeCircle(playerRadius, playerRadius, playerRadius);
        team1Graphics.generateTexture('player-team1', diameter, diameter);
        team1Graphics.destroy();
        
        // Team 2 (Red)
        const team2Graphics = this.add.graphics();
        team2Graphics.fillStyle(GameConfig.PLAYER.TEAM_2_COLOR);
        team2Graphics.fillCircle(playerRadius, playerRadius, playerRadius);
        team2Graphics.lineStyle(2, 0xFFFFFF);
        team2Graphics.strokeCircle(playerRadius, playerRadius, playerRadius);
        team2Graphics.generateTexture('player-team2', diameter, diameter);
        team2Graphics.destroy();
        
        // Goalkeeper (Orange)
        const goalkeeperGraphics = this.add.graphics();
        goalkeeperGraphics.fillStyle(GameConfig.PLAYER.GOALKEEPER_COLOR);
        goalkeeperGraphics.fillCircle(playerRadius, playerRadius, playerRadius);
        goalkeeperGraphics.lineStyle(3, 0xFFFFFF);
        goalkeeperGraphics.strokeCircle(playerRadius, playerRadius, playerRadius);
        goalkeeperGraphics.generateTexture('player-goalkeeper', diameter, diameter);
        goalkeeperGraphics.destroy();
        
        // Selected player indicator
        const selectedGraphics = this.add.graphics();
        selectedGraphics.lineStyle(4, 0xFFFF00);
        selectedGraphics.strokeCircle(playerRadius, playerRadius, playerRadius + 4);
        selectedGraphics.generateTexture('player-selected', diameter + 8, diameter + 8);
        selectedGraphics.destroy();
    }
    
    generateBallSprite() {
        const ballRadius = GameConfig.BALL.RADIUS;
        const diameter = ballRadius * 2;
        
        const ballGraphics = this.add.graphics();
        ballGraphics.fillStyle(GameConfig.BALL.COLOR);
        ballGraphics.fillCircle(ballRadius, ballRadius, ballRadius);
        ballGraphics.lineStyle(2, 0xFFFFFF);
        ballGraphics.strokeCircle(ballRadius, ballRadius, ballRadius);
        
        // Add some detail lines to make it look more like a water polo ball
        ballGraphics.lineStyle(1, 0xFFFFFF, 0.7);
        ballGraphics.beginPath();
        ballGraphics.moveTo(ballRadius, 0);
        ballGraphics.lineTo(ballRadius, diameter);
        ballGraphics.moveTo(0, ballRadius);
        ballGraphics.lineTo(diameter, ballRadius);
        ballGraphics.strokePath();
        
        ballGraphics.generateTexture('ball', diameter, diameter);
        ballGraphics.destroy();
    }
    
    generateUIElements() {
        // Button background
        const buttonGraphics = this.add.graphics();
        buttonGraphics.fillStyle(0x3498DB);
        buttonGraphics.fillRoundedRect(0, 0, 200, 50, 10);
        buttonGraphics.lineStyle(2, 0xFFFFFF);
        buttonGraphics.strokeRoundedRect(0, 0, 200, 50, 10);
        buttonGraphics.generateTexture('button', 200, 50);
        buttonGraphics.destroy();
        
        // Panel background
        const panelGraphics = this.add.graphics();
        panelGraphics.fillStyle(0x2C3E50, 0.9);
        panelGraphics.fillRoundedRect(0, 0, 300, 200, 15);
        panelGraphics.lineStyle(3, 0x3498DB);
        panelGraphics.strokeRoundedRect(0, 0, 300, 200, 15);
        panelGraphics.generateTexture('panel', 300, 200);
        panelGraphics.destroy();
        
        // HUD background
        const hudGraphics = this.add.graphics();
        hudGraphics.fillStyle(0x2C3E50, 0.8);
        hudGraphics.fillRect(0, 0, GameConfig.CANVAS_WIDTH, GameConfig.UI.HUD_HEIGHT);
        hudGraphics.generateTexture('hud-bg', GameConfig.CANVAS_WIDTH, GameConfig.UI.HUD_HEIGHT);
        hudGraphics.destroy();
    }
    
    generatePoolElements() {
        // Goal posts
        const goalGraphics = this.add.graphics();
        goalGraphics.fillStyle(GameConfig.POOL.GOAL_COLOR);
        goalGraphics.fillRect(0, 0, GameConfig.POOL.GOAL_WIDTH, GameConfig.POOL.GOAL_HEIGHT);
        goalGraphics.lineStyle(3, 0xFFFFFF);
        goalGraphics.strokeRect(0, 0, GameConfig.POOL.GOAL_WIDTH, GameConfig.POOL.GOAL_HEIGHT);
        goalGraphics.generateTexture('goal', GameConfig.POOL.GOAL_WIDTH, GameConfig.POOL.GOAL_HEIGHT);
        goalGraphics.destroy();
        
        // Pool lane markers
        const laneGraphics = this.add.graphics();
        laneGraphics.lineStyle(2, GameConfig.POOL.LANE_COLOR, 0.8);
        laneGraphics.beginPath();
        laneGraphics.moveTo(0, 0);
        laneGraphics.lineTo(100, 0);
        laneGraphics.strokePath();
        laneGraphics.generateTexture('lane-marker', 100, 2);
        laneGraphics.destroy();
        
        // Zone markers
        const zoneGraphics = this.add.graphics();
        zoneGraphics.lineStyle(3, GameConfig.POOL.ZONE_COLOR, 0.6);
        zoneGraphics.strokeRect(0, 0, 100, 100);
        zoneGraphics.generateTexture('zone-marker', 100, 100);
        zoneGraphics.destroy();
    }
    
    generatePowerUpSprites() {
        // Turbo power-up
        const turboGraphics = this.add.graphics();
        turboGraphics.fillStyle(0x00FF00);
        turboGraphics.fillStar(15, 15, 5, 10, 15);
        turboGraphics.lineStyle(2, 0xFFFFFF);
        turboGraphics.strokeStar(15, 15, 5, 10, 15);
        turboGraphics.generateTexture('powerup-turbo', 30, 30);
        turboGraphics.destroy();
        
        // Fast shot power-up
        const fastShotGraphics = this.add.graphics();
        fastShotGraphics.fillStyle(0xFF0000);
        fastShotGraphics.fillStar(15, 15, 5, 10, 15);
        fastShotGraphics.lineStyle(2, 0xFFFFFF);
        fastShotGraphics.strokeStar(15, 15, 5, 10, 15);
        fastShotGraphics.generateTexture('powerup-fastshot', 30, 30);
        fastShotGraphics.destroy();
    }
}
