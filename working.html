<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Water Polo Clash - Working Version</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        #game-container {
            border: 3px solid #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
        }
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            text-align: center;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div class="loading" id="loading">Loading Water Polo Clash...</div>
        <canvas id="game-canvas"></canvas>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    
    <script>
        console.log('🎮 Starting Water Polo Clash - Working Version');
        
        // Game configuration
        const GameConfig = {
            CANVAS_WIDTH: 1024,
            CANVAS_HEIGHT: 640,
            POOL: {
                WIDTH: 800,
                HEIGHT: 500,
                WATER_COLOR: 0x4A90E2,
                GOAL_WIDTH: 120,
                GOAL_HEIGHT: 20
            },
            PLAYER: {
                RADIUS: 15,
                TEAM_1_COLOR: 0x3498DB,
                TEAM_2_COLOR: 0xE74C3C,
                GOALKEEPER_COLOR: 0xF39C12
            },
            BALL: {
                RADIUS: 10,
                COLOR: 0xFFD700
            }
        };
        
        // Preload Scene
        class PreloadScene extends Phaser.Scene {
            constructor() {
                super({ key: 'PreloadScene' });
            }
            
            preload() {
                console.log('PreloadScene: Loading assets...');
                
                // Create loading bar
                const width = this.cameras.main.width;
                const height = this.cameras.main.height;
                
                this.add.rectangle(width / 2, height / 2, width, height, 0x2C3E50);
                this.add.text(width / 2, height / 2 - 50, 'WATER POLO CLASH', {
                    fontSize: '32px',
                    color: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                
                const loadingText = this.add.text(width / 2, height / 2 + 50, 'Loading...', {
                    fontSize: '18px',
                    color: '#ffffff'
                }).setOrigin(0.5);
                
                // Generate simple assets
                this.generateAssets();
                
                // Simulate loading time
                this.time.delayedCall(1000, () => {
                    console.log('PreloadScene: Loading complete');
                    this.scene.start('MainMenuScene');
                });
            }
            
            generateAssets() {
                // Player sprites
                const graphics = this.add.graphics();
                
                // Team 1 player
                graphics.fillStyle(GameConfig.PLAYER.TEAM_1_COLOR);
                graphics.fillCircle(15, 15, 15);
                graphics.generateTexture('player-team1', 30, 30);
                
                // Team 2 player
                graphics.clear();
                graphics.fillStyle(GameConfig.PLAYER.TEAM_2_COLOR);
                graphics.fillCircle(15, 15, 15);
                graphics.generateTexture('player-team2', 30, 30);
                
                // Goalkeeper
                graphics.clear();
                graphics.fillStyle(GameConfig.PLAYER.GOALKEEPER_COLOR);
                graphics.fillCircle(15, 15, 15);
                graphics.generateTexture('player-goalkeeper', 30, 30);
                
                // Ball
                graphics.clear();
                graphics.fillStyle(GameConfig.BALL.COLOR);
                graphics.fillCircle(10, 10, 10);
                graphics.generateTexture('ball', 20, 20);
                
                graphics.destroy();
                console.log('Assets generated successfully');
            }
        }
        
        // Main Menu Scene
        class MainMenuScene extends Phaser.Scene {
            constructor() {
                super({ key: 'MainMenuScene' });
            }
            
            create() {
                console.log('MainMenuScene: Creating menu...');
                
                const width = this.cameras.main.width;
                const height = this.cameras.main.height;
                
                // Background
                this.add.rectangle(width / 2, height / 2, width, height, 0x1e3c72);
                
                // Title
                this.add.text(width / 2, height / 2 - 100, 'WATER POLO CLASH', {
                    fontSize: '48px',
                    color: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                
                // Menu buttons
                const startButton = this.add.text(width / 2, height / 2, 'START GAME', {
                    fontSize: '24px',
                    color: '#3498DB',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                
                startButton.setInteractive();
                startButton.on('pointerover', () => startButton.setScale(1.1));
                startButton.on('pointerout', () => startButton.setScale(1));
                startButton.on('pointerdown', () => {
                    console.log('Starting game...');
                    this.scene.start('GameScene');
                });
                
                // Instructions
                this.add.text(width / 2, height / 2 + 100, 'WASD: Move | SPACE: Action | TAB: Switch Player', {
                    fontSize: '16px',
                    color: '#ffffff',
                    alpha: 0.8
                }).setOrigin(0.5);
                
                console.log('MainMenuScene: Menu created successfully');
            }
        }
        
        // Game Scene
        class GameScene extends Phaser.Scene {
            constructor() {
                super({ key: 'GameScene' });
            }
            
            init() {
                this.players = [];
                this.ball = null;
                this.score = [0, 0];
                this.gameTimer = 120; // 2 minutes
                this.currentPlayer = null;
            }
            
            create() {
                console.log('GameScene: Creating game...');
                
                const width = this.cameras.main.width;
                const height = this.cameras.main.height;
                
                // Pool
                this.createPool();
                
                // Players
                this.createPlayers();
                
                // Ball
                this.createBall();
                
                // HUD
                this.createHUD();
                
                // Input
                this.setupInput();
                
                console.log('GameScene: Game created successfully');
            }
            
            createPool() {
                const width = this.cameras.main.width;
                const height = this.cameras.main.height;
                const poolX = (width - GameConfig.POOL.WIDTH) / 2;
                const poolY = (height - GameConfig.POOL.HEIGHT) / 2;
                
                // Pool background
                this.add.rectangle(width / 2, height / 2, GameConfig.POOL.WIDTH, GameConfig.POOL.HEIGHT, GameConfig.POOL.WATER_COLOR);
                
                // Pool border
                this.add.rectangle(width / 2, height / 2, GameConfig.POOL.WIDTH, GameConfig.POOL.HEIGHT)
                    .setStrokeStyle(4, 0xFFFFFF);
                
                // Goals
                this.add.rectangle(poolX, height / 2, GameConfig.POOL.GOAL_HEIGHT, GameConfig.POOL.GOAL_WIDTH, 0xFF6B6B);
                this.add.rectangle(poolX + GameConfig.POOL.WIDTH, height / 2, GameConfig.POOL.GOAL_HEIGHT, GameConfig.POOL.GOAL_WIDTH, 0xFF6B6B);
                
                // Center line
                this.add.line(width / 2, height / 2, 0, -GameConfig.POOL.HEIGHT/2, 0, GameConfig.POOL.HEIGHT/2, 0xFFFFFF, 0.5)
                    .setLineWidth(2);
            }
            
            createPlayers() {
                const width = this.cameras.main.width;
                const height = this.cameras.main.height;
                
                // Team 1 (Blue) - Player's team
                for (let i = 0; i < 7; i++) {
                    const x = width / 2 + 100 + (i % 3) * 60;
                    const y = height / 2 - 100 + Math.floor(i / 3) * 100;
                    const isGoalkeeper = i === 0;
                    
                    const player = this.add.image(x, y, isGoalkeeper ? 'player-goalkeeper' : 'player-team1');
                    player.team = 0;
                    player.number = i + 1;
                    player.isSelected = false;
                    
                    // Add number
                    this.add.text(x, y, (i + 1).toString(), {
                        fontSize: '12px',
                        color: '#ffffff',
                        fontStyle: 'bold'
                    }).setOrigin(0.5);
                    
                    this.players.push(player);
                }
                
                // Team 2 (Red) - AI team
                for (let i = 0; i < 7; i++) {
                    const x = width / 2 - 100 - (i % 3) * 60;
                    const y = height / 2 - 100 + Math.floor(i / 3) * 100;
                    const isGoalkeeper = i === 0;
                    
                    const player = this.add.image(x, y, isGoalkeeper ? 'player-goalkeeper' : 'player-team2');
                    player.team = 1;
                    player.number = i + 1;
                    
                    // Add number
                    this.add.text(x, y, (i + 1).toString(), {
                        fontSize: '12px',
                        color: '#ffffff',
                        fontStyle: 'bold'
                    }).setOrigin(0.5);
                    
                    this.players.push(player);
                }
                
                // Select first player
                this.currentPlayer = this.players[1]; // Skip goalkeeper
                this.currentPlayer.setTint(0xFFFF00);
                this.currentPlayer.isSelected = true;
            }
            
            createBall() {
                const width = this.cameras.main.width;
                const height = this.cameras.main.height;
                
                this.ball = this.add.image(width / 2, height / 2, 'ball');
            }
            
            createHUD() {
                const width = this.cameras.main.width;
                
                // HUD background
                this.add.rectangle(width / 2, 40, width, 60, 0x2C3E50, 0.8);
                
                // Score
                this.scoreText = this.add.text(width / 2, 40, '0 - 0', {
                    fontSize: '24px',
                    color: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                
                // Timer
                this.timerText = this.add.text(width / 2, 20, '02:00', {
                    fontSize: '16px',
                    color: '#ffffff'
                }).setOrigin(0.5);
                
                // Instructions
                this.add.text(width / 2, 60, 'WASD: Move | SPACE: Action | TAB: Switch | ESC: Menu', {
                    fontSize: '12px',
                    color: '#ffffff',
                    alpha: 0.7
                }).setOrigin(0.5);
            }
            
            setupInput() {
                this.cursors = this.input.keyboard.createCursorKeys();
                this.wasd = this.input.keyboard.addKeys('W,S,A,D');
                this.spaceKey = this.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.SPACE);
                this.tabKey = this.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.TAB);
                this.escKey = this.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.ESC);
            }
            
            update() {
                // Handle input
                this.handleInput();
                
                // Update timer
                this.updateTimer();
            }
            
            handleInput() {
                if (!this.currentPlayer) return;
                
                // Movement
                let moveX = 0;
                let moveY = 0;
                
                if (this.wasd.W.isDown) moveY -= 2;
                if (this.wasd.S.isDown) moveY += 2;
                if (this.wasd.A.isDown) moveX -= 2;
                if (this.wasd.D.isDown) moveX += 2;
                
                if (moveX !== 0 || moveY !== 0) {
                    this.currentPlayer.x += moveX;
                    this.currentPlayer.y += moveY;
                    
                    // Keep player in bounds
                    const bounds = this.cameras.main.getBounds();
                    this.currentPlayer.x = Phaser.Math.Clamp(this.currentPlayer.x, 50, bounds.width - 50);
                    this.currentPlayer.y = Phaser.Math.Clamp(this.currentPlayer.y, 100, bounds.height - 50);
                }
                
                // Switch player
                if (Phaser.Input.Keyboard.JustDown(this.tabKey)) {
                    this.switchPlayer();
                }
                
                // Action
                if (Phaser.Input.Keyboard.JustDown(this.spaceKey)) {
                    this.performAction();
                }
                
                // Back to menu
                if (Phaser.Input.Keyboard.JustDown(this.escKey)) {
                    this.scene.start('MainMenuScene');
                }
            }
            
            switchPlayer() {
                if (this.currentPlayer) {
                    this.currentPlayer.clearTint();
                    this.currentPlayer.isSelected = false;
                }
                
                // Find next player on team 1
                const team1Players = this.players.filter(p => p.team === 0);
                const currentIndex = team1Players.indexOf(this.currentPlayer);
                const nextIndex = (currentIndex + 1) % team1Players.length;
                
                this.currentPlayer = team1Players[nextIndex];
                this.currentPlayer.setTint(0xFFFF00);
                this.currentPlayer.isSelected = true;
                
                console.log(`Switched to player ${this.currentPlayer.number}`);
            }
            
            performAction() {
                // Simple action - move ball towards goal
                const goalX = this.cameras.main.width - 100;
                const goalY = this.cameras.main.height / 2;
                
                this.tweens.add({
                    targets: this.ball,
                    x: goalX + (Math.random() - 0.5) * 100,
                    y: goalY + (Math.random() - 0.5) * 60,
                    duration: 1000,
                    ease: 'Power2'
                });
                
                console.log(`Player ${this.currentPlayer.number} performed action`);
            }
            
            updateTimer() {
                if (this.gameTimer > 0) {
                    this.gameTimer -= 1/60; // Assuming 60 FPS
                    const minutes = Math.floor(this.gameTimer / 60);
                    const seconds = Math.floor(this.gameTimer % 60);
                    this.timerText.setText(`${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);
                } else {
                    // Game over
                    this.add.text(this.cameras.main.width / 2, this.cameras.main.height / 2, 'GAME OVER', {
                        fontSize: '48px',
                        color: '#ffffff',
                        fontStyle: 'bold'
                    }).setOrigin(0.5);
                }
            }
        }
        
        // Game configuration
        const config = {
            type: Phaser.AUTO,
            width: GameConfig.CANVAS_WIDTH,
            height: GameConfig.CANVAS_HEIGHT,
            canvas: document.getElementById('game-canvas'),
            scene: [PreloadScene, MainMenuScene, GameScene],
            backgroundColor: 0x1e3c72,
            scale: {
                mode: Phaser.Scale.FIT,
                autoCenter: Phaser.Scale.CENTER_BOTH
            }
        };
        
        // Hide loading screen
        document.getElementById('loading').style.display = 'none';
        
        // Initialize game
        try {
            console.log('Initializing Phaser game...');
            const game = new Phaser.Game(config);
            console.log('✅ Water Polo Clash initialized successfully!');
        } catch (error) {
            console.error('❌ Error initializing game:', error);
        }
    </script>
</body>
</html>
