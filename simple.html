<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Water Polo Clash - Simple Version</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        #game-container {
            border: 3px solid #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
        }
    </style>
</head>
<body>
    <div id="game-container">
        <canvas id="game-canvas"></canvas>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    
    <script>
        console.log('Starting simple Water Polo game...');
        
        // Simple scene that just shows the menu
        class SimpleMenuScene extends Phaser.Scene {
            constructor() {
                super({ key: 'SimpleMenuScene' });
            }
            
            create() {
                console.log('SimpleMenuScene: Creating...');
                
                const width = this.cameras.main.width;
                const height = this.cameras.main.height;
                
                // Background
                this.add.rectangle(width / 2, height / 2, width, height, 0x1e3c72);
                
                // Title
                this.add.text(width / 2, height / 2 - 100, 'WATER POLO CLASH', {
                    fontSize: '48px',
                    fontFamily: 'Arial',
                    color: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                
                // Subtitle
                this.add.text(width / 2, height / 2 - 40, 'Simple Version Working!', {
                    fontSize: '24px',
                    fontFamily: 'Arial',
                    color: '#3498DB'
                }).setOrigin(0.5);
                
                // Instructions
                this.add.text(width / 2, height / 2 + 40, 'Click to start game', {
                    fontSize: '18px',
                    fontFamily: 'Arial',
                    color: '#ffffff'
                }).setOrigin(0.5);
                
                // Make it interactive
                this.input.on('pointerdown', () => {
                    this.scene.start('SimpleGameScene');
                });
                
                console.log('SimpleMenuScene: Created successfully');
            }
        }
        
        // Simple game scene with basic water polo elements
        class SimpleGameScene extends Phaser.Scene {
            constructor() {
                super({ key: 'SimpleGameScene' });
            }
            
            create() {
                console.log('SimpleGameScene: Creating...');
                
                const width = this.cameras.main.width;
                const height = this.cameras.main.height;
                
                // Pool background
                this.add.rectangle(width / 2, height / 2, 800, 500, 0x4A90E2);
                
                // Pool border
                this.add.rectangle(width / 2, height / 2, 800, 500).setStrokeStyle(4, 0xFFFFFF);
                
                // Goals
                this.add.rectangle(width / 2 - 400, height / 2, 20, 120, 0xFF6B6B);
                this.add.rectangle(width / 2 + 400, height / 2, 20, 120, 0xFF6B6B);
                
                // Center line
                this.add.line(width / 2, height / 2, 0, -250, 0, 250, 0xFFFFFF, 0.5).setLineWidth(2);
                
                // Players (simple circles)
                // Team 1 (Blue)
                for (let i = 0; i < 7; i++) {
                    const x = width / 2 + 100 + (i % 3) * 60;
                    const y = height / 2 - 100 + Math.floor(i / 3) * 100;
                    this.add.circle(x, y, 15, 0x3498DB).setStrokeStyle(2, 0xFFFFFF);
                    this.add.text(x, y, (i + 1).toString(), {
                        fontSize: '12px',
                        color: '#ffffff',
                        fontStyle: 'bold'
                    }).setOrigin(0.5);
                }
                
                // Team 2 (Red)
                for (let i = 0; i < 7; i++) {
                    const x = width / 2 - 100 - (i % 3) * 60;
                    const y = height / 2 - 100 + Math.floor(i / 3) * 100;
                    this.add.circle(x, y, 15, 0xE74C3C).setStrokeStyle(2, 0xFFFFFF);
                    this.add.text(x, y, (i + 1).toString(), {
                        fontSize: '12px',
                        color: '#ffffff',
                        fontStyle: 'bold'
                    }).setOrigin(0.5);
                }
                
                // Ball
                this.add.circle(width / 2, height / 2, 10, 0xFFD700).setStrokeStyle(2, 0xFFFFFF);
                
                // HUD
                this.add.rectangle(width / 2, 40, width, 60, 0x2C3E50, 0.8);
                this.add.text(width / 2 - 100, 40, 'PLAYER: 0', {
                    fontSize: '20px',
                    color: '#3498DB',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                
                this.add.text(width / 2, 40, 'VS', {
                    fontSize: '16px',
                    color: '#ffffff'
                }).setOrigin(0.5);
                
                this.add.text(width / 2 + 100, 40, 'AI: 0', {
                    fontSize: '20px',
                    color: '#E74C3C',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                
                // Timer
                this.add.text(width / 2, 20, '02:00 - Q1', {
                    fontSize: '14px',
                    color: '#ffffff'
                }).setOrigin(0.5);
                
                // Instructions
                this.add.text(width / 2, height - 30, 'ESC: Back to Menu | WASD: Move | SPACE: Action', {
                    fontSize: '12px',
                    color: '#ffffff',
                    alpha: 0.7
                }).setOrigin(0.5);
                
                // Input
                this.cursors = this.input.keyboard.createCursorKeys();
                this.escKey = this.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.ESC);
                
                console.log('SimpleGameScene: Created successfully');
            }
            
            update() {
                if (Phaser.Input.Keyboard.JustDown(this.escKey)) {
                    this.scene.start('SimpleMenuScene');
                }
            }
        }
        
        // Game configuration
        const config = {
            type: Phaser.AUTO,
            width: 1024,
            height: 640,
            canvas: document.getElementById('game-canvas'),
            scene: [SimpleMenuScene, SimpleGameScene],
            backgroundColor: 0x1e3c72,
            scale: {
                mode: Phaser.Scale.FIT,
                autoCenter: Phaser.Scale.CENTER_BOTH
            }
        };
        
        // Initialize game
        try {
            console.log('Initializing simple Phaser game...');
            const game = new Phaser.Game(config);
            console.log('Simple game initialized successfully!');
        } catch (error) {
            console.error('Error initializing simple game:', error);
        }
    </script>
</body>
</html>
