<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Water Polo Clash - Main Game Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1e3c72;
            color: white;
            font-family: Arial, sans-serif;
        }
        #debug-output {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
        .error { color: #ff6b6b; }
        .success { color: #51cf66; }
        .info { color: #74c0fc; }
        #game-container {
            border: 3px solid #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
            background: #000;
            display: inline-block;
        }
    </style>
</head>
<body>
    <h1>🔧 Water Polo Clash - Main Game Test</h1>
    
    <div id="debug-output">
        <div>Starting main game test...</div>
    </div>
    
    <div id="game-container">
        <canvas id="game-canvas"></canvas>
    </div>

    <!-- Load Phaser first -->
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    
    <!-- Load game files in order -->
    <script src="js/config.js"></script>
    <script src="js/scenes/PreloadScene.js"></script>
    <script src="js/scenes/MainMenuScene.js"></script>
    <script src="js/scenes/GameScene.js"></script>
    <script src="js/entities/Player.js"></script>
    <script src="js/entities/Ball.js"></script>
    <script src="js/systems/InputManager.js"></script>
    <script src="js/systems/GameRules.js"></script>
    <script src="js/systems/AIManager.js"></script>
    <script src="js/systems/PowerUpManager.js"></script>
    <script src="js/ui/HUD.js"></script>
    <script src="js/main.js"></script>
    
    <script>
        const debugOutput = document.getElementById('debug-output');
        
        function log(message, type = 'info') {
            console.log(message);
            const div = document.createElement('div');
            div.className = type;
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            debugOutput.appendChild(div);
            debugOutput.scrollTop = debugOutput.scrollHeight;
        }
        
        // Override console.error to catch errors
        const originalError = console.error;
        console.error = function(...args) {
            log('ERROR: ' + args.join(' '), 'error');
            originalError.apply(console, args);
        };
        
        // Override console.warn to catch warnings
        const originalWarn = console.warn;
        console.warn = function(...args) {
            log('WARNING: ' + args.join(' '), 'info');
            originalWarn.apply(console, args);
        };
        
        // Catch unhandled errors
        window.addEventListener('error', function(e) {
            log('UNHANDLED ERROR: ' + e.message + ' at ' + e.filename + ':' + e.lineno, 'error');
        });
        
        // Test if all classes are defined
        setTimeout(() => {
            log('Testing class definitions...', 'info');
            
            const classes = [
                'GameConfig', 'GameUtils', 'GameStates', 'Teams', 'PlayerPositions',
                'PreloadScene', 'MainMenuScene', 'GameScene',
                'Player', 'Ball',
                'InputManager', 'GameRules', 'AIManager', 'PowerUpManager',
                'HUD', 'WaterPoloGame'
            ];
            
            let allDefined = true;
            classes.forEach(className => {
                if (typeof window[className] !== 'undefined') {
                    log('✅ ' + className + ' is defined', 'success');
                } else {
                    log('❌ ' + className + ' is NOT defined', 'error');
                    allDefined = false;
                }
            });
            
            if (allDefined) {
                log('✅ All classes defined successfully!', 'success');
                log('🎮 Game should be initializing...', 'info');
            } else {
                log('❌ Some classes are missing - game may not work', 'error');
            }
        }, 1000);
    </script>
</body>
</html>
