<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Water Polo Clash - Debug</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1e3c72;
            color: white;
            font-family: Arial, sans-serif;
        }
        #debug-info {
            margin-bottom: 20px;
            padding: 10px;
            background: rgba(0,0,0,0.3);
            border-radius: 5px;
        }
        #game-container {
            border: 2px solid white;
            display: inline-block;
        }
        .error {
            color: #ff6b6b;
            background: rgba(255,0,0,0.1);
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            color: #51cf66;
            background: rgba(0,255,0,0.1);
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>🔧 Water Polo Clash - Debug Mode</h1>
    
    <div id="debug-info">
        <h3>Debug Information:</h3>
        <div id="debug-output">Initializing...</div>
    </div>
    
    <div id="game-container">
        <canvas id="game-canvas" width="1024" height="640"></canvas>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    
    <script>
        const debugOutput = document.getElementById('debug-output');
        
        function log(message, type = 'info') {
            console.log(message);
            const div = document.createElement('div');
            div.className = type;
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            debugOutput.appendChild(div);
        }
        
        // Test 1: Check if Phaser loaded
        if (typeof Phaser !== 'undefined') {
            log('✅ Phaser 3 loaded successfully (version: ' + Phaser.VERSION + ')', 'success');
        } else {
            log('❌ Phaser 3 failed to load', 'error');
        }
        
        // Test 2: Check canvas
        const canvas = document.getElementById('game-canvas');
        if (canvas) {
            log('✅ Canvas element found', 'success');
        } else {
            log('❌ Canvas element not found', 'error');
        }
        
        // Test 3: Simple Phaser test
        class TestScene extends Phaser.Scene {
            constructor() {
                super({ key: 'TestScene' });
            }
            
            create() {
                log('✅ TestScene created successfully', 'success');
                
                // Add background
                this.add.rectangle(512, 320, 1024, 640, 0x4A90E2);
                
                // Add title
                this.add.text(512, 200, 'WATER POLO CLASH', {
                    fontSize: '48px',
                    fontFamily: 'Arial',
                    color: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                
                // Add subtitle
                this.add.text(512, 280, 'Debug Mode - Phaser Working!', {
                    fontSize: '24px',
                    fontFamily: 'Arial',
                    color: '#ffffff'
                }).setOrigin(0.5);
                
                // Add instructions
                this.add.text(512, 400, 'If you see this text, Phaser is working correctly.\nThe main game should load properly.', {
                    fontSize: '18px',
                    fontFamily: 'Arial',
                    color: '#ffffff',
                    align: 'center'
                }).setOrigin(0.5);
                
                log('✅ All test elements added to scene', 'success');
            }
        }
        
        // Test 4: Initialize minimal Phaser game
        try {
            log('🎮 Initializing test Phaser game...', 'info');
            
            const config = {
                type: Phaser.AUTO,
                width: 1024,
                height: 640,
                canvas: canvas,
                scene: TestScene,
                backgroundColor: 0x1e3c72
            };
            
            const game = new Phaser.Game(config);
            log('✅ Test Phaser game initialized successfully', 'success');
            
        } catch (error) {
            log('❌ Error initializing Phaser game: ' + error.message, 'error');
            console.error('Full error:', error);
        }
        
        // Test 5: Check for common issues
        setTimeout(() => {
            log('🔍 Running additional checks...', 'info');

            // Check if WebGL is available
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (gl) {
                log('✅ WebGL is available', 'success');
            } else {
                log('⚠️ WebGL not available, falling back to Canvas', 'info');
            }

            // Check canvas size
            log('📏 Canvas size: ' + canvas.width + 'x' + canvas.height, 'info');

            // Test main game files
            log('🔍 Testing main game files...', 'info');
            testMainGameFiles();

            log('🎯 Debug test complete. Check above for any errors.', 'info');
        }, 2000);

        function testMainGameFiles() {
            // Test if main game files can be loaded
            const testFiles = [
                'js/config.js',
                'js/main.js',
                'js/scenes/PreloadScene.js',
                'js/scenes/MainMenuScene.js',
                'js/scenes/GameScene.js',
                'js/entities/Player.js',
                'js/entities/Ball.js',
                'js/systems/InputManager.js',
                'js/systems/GameRules.js',
                'js/systems/AIManager.js',
                'js/systems/PowerUpManager.js',
                'js/ui/HUD.js'
            ];

            let loadedCount = 0;
            let errorCount = 0;

            testFiles.forEach(file => {
                fetch(file)
                    .then(response => {
                        if (response.ok) {
                            loadedCount++;
                            log('✅ ' + file + ' - OK', 'success');
                        } else {
                            errorCount++;
                            log('❌ ' + file + ' - Failed to load', 'error');
                        }

                        if (loadedCount + errorCount === testFiles.length) {
                            if (errorCount === 0) {
                                log('✅ All main game files loaded successfully!', 'success');
                                log('🎮 Try loading the main game: <a href="index.html" target="_blank">index.html</a>', 'info');
                            } else {
                                log('❌ ' + errorCount + ' files failed to load', 'error');
                            }
                        }
                    })
                    .catch(error => {
                        errorCount++;
                        log('❌ ' + file + ' - Error: ' + error.message, 'error');
                    });
            });
        }
    </script>
</body>
</html>
